# Twitter Bot for @OcliqOfficial

An intelligent Twitter/X bot that automatically generates and posts engaging content for the Ocliq brand using Google's Gemini AI.

## Features

- 🤖 **AI-Powered Content**: Uses Gemini AI to generate 5 different types of engaging tweets
- 📊 **Smart Rate Limiting**: Posts max 2 tweets/hour, 48/day with randomized timing
- 🔄 **Content Rotation**: Alternates between AI tips, mini-threads, showcases, CTAs, and stats
- 📝 **Duplicate Prevention**: Prevents posting similar content
- 📈 **Comprehensive Logging**: Tracks all posted tweets and bot activity
- ⚙️ **Flexible Execution**: Run continuously or via cron scheduling
- 🔍 **Preview Mode**: Preview tweets before posting

## Content Types

1. **💡 AI Web Tips** - Practical AI advice for web development
2. **🧠 Mini-Threads** - 2-3 part educational content about SEO/web strategy  
3. **🖥️ Showcase Posts** - Highlight Ocliq's success stories and results
4. **🚀 CTAs** - Call-to-action tweets with link to https://ocliq.com
5. **📊 Stats** - Compelling statistics about web design and mobile optimization

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Get API Credentials

#### Twitter/X API:
1. Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)
2. Create a new app or use existing one
3. Generate API keys and tokens with Read/Write permissions
4. Note down:
   - Bearer Token
   - Consumer Key & Secret
   - Access Token & Secret

#### Gemini AI API:
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Note down the API key

### 3. Configure Environment Variables

```bash
# Copy the example file
cp .env.example .env

# Edit .env with your actual credentials
nano .env
```

Fill in your actual API credentials in the `.env` file.

### 4. Test the Bot

```bash
# Check bot status
python twitterbot.py --mode status

# Generate some tweets (without posting)
python twitterbot.py --mode generate --count 5

# Preview next tweet
python twitterbot.py --preview
```

## Usage

### Run Continuously
```bash
python twitterbot.py --mode continuous
```
This runs the bot continuously, posting tweets automatically with random delays.

### Run Once (for Cron)
```bash
python twitterbot.py --mode scheduled
```
This attempts to post one tweet and exits. Perfect for cron scheduling.

### Cron Setup Example
Add to your crontab to run every 30 minutes:
```bash
# Edit crontab
crontab -e

# Add this line (adjust path as needed)
*/30 * * * * cd /path/to/twitter-bot && python twitterbot.py --mode scheduled >> cron.log 2>&1
```

### Check Status
```bash
python twitterbot.py --mode status
```

### Generate Tweets Only
```bash
python twitterbot.py --mode generate --count 20
```

## Configuration

The bot includes several configurable parameters in the `TwitterBot` class:

- `max_tweets_per_hour = 2` - Maximum tweets per hour
- `max_tweets_per_day = 48` - Maximum tweets per day  
- `min_delay_minutes = 20` - Minimum delay between tweets
- `max_delay_minutes = 40` - Maximum delay between tweets
- `min_queue_size = 10` - Minimum tweets in queue before regenerating

## Files Created

- `tweets_log.json` - Log of all posted tweets
- `twitter_bot.log` - Bot activity log
- `cron.log` - Cron execution log (if using cron)

## Safety Features

- **Rate Limiting**: Respects Twitter's rate limits and your configured limits
- **Duplicate Detection**: Prevents posting similar content
- **Error Handling**: Graceful error handling with retries
- **Logging**: Comprehensive logging for monitoring and debugging

## Troubleshooting

### Common Issues

1. **Authentication Error**: Check your API credentials in `.env`
2. **Rate Limit**: Bot will automatically wait if rate limits are hit
3. **No Tweets Generated**: Check Gemini API key and internet connection
4. **Permission Denied**: Ensure Twitter app has Read/Write permissions

### Logs
Check `twitter_bot.log` for detailed error messages and bot activity.

## License

This project is for Ocliq's internal use.
