#!/usr/bin/env python3
"""
Twitter/X Bot for @OcliqOfficial
Automatically generates and posts tweets using Gemini AI
"""

import os
import json
import time
import random
import logging
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import tweepy
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class TwitterBot:
    def __init__(self):
        """Initialize the Twitter bot with API credentials and configuration"""
        self.setup_logging()
        self.setup_twitter_api()
        self.setup_gemini_api()

        # Configuration
        self.max_tweets_per_hour = 2
        self.max_tweets_per_day = 48
        self.min_delay_minutes = 20
        self.max_delay_minutes = 40
        self.min_queue_size = 10

        # Tweet tracking
        self.tweets_log_file = "tweets_log.json"
        self.posted_tweets = self.load_posted_tweets()
        self.tweet_queue = []

        # Content types for rotation
        self.content_types = [
            "ai_web_tips",
            "mini_threads",
            "showcase_posts",
            "ctas",
            "stats"
        ]
        self.current_content_index = 0

        # Rate limiting tracking
        self.tweets_posted_today = self.count_todays_tweets()
        self.last_tweet_time = None

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('twitter_bot.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_twitter_api(self):
        """Initialize Twitter API connection"""
        try:
            # Twitter API v2 credentials
            bearer_token = os.getenv('TWITTER_BEARER_TOKEN')
            consumer_key = os.getenv('TWITTER_CONSUMER_KEY')
            consumer_secret = os.getenv('TWITTER_CONSUMER_SECRET')
            access_token = os.getenv('TWITTER_ACCESS_TOKEN')
            access_token_secret = os.getenv('TWITTER_ACCESS_TOKEN_SECRET')

            if not all([bearer_token, consumer_key, consumer_secret, access_token, access_token_secret]):
                raise ValueError("Missing Twitter API credentials in environment variables")

            # Initialize Tweepy client
            self.twitter_client = tweepy.Client(
                bearer_token=bearer_token,
                consumer_key=consumer_key,
                consumer_secret=consumer_secret,
                access_token=access_token,
                access_token_secret=access_token_secret,
                wait_on_rate_limit=True
            )

            # Test authentication
            me = self.twitter_client.get_me()
            self.logger.info(f"Twitter API authenticated successfully for user: {me.data.username}")

        except Exception as e:
            self.logger.error(f"Failed to setup Twitter API: {e}")
            raise

    def setup_gemini_api(self):
        """Initialize Gemini API connection"""
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        if not self.gemini_api_key:
            raise ValueError("Missing GEMINI_API_KEY in environment variables")

        self.gemini_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
        self.logger.info("Gemini API configured successfully")

    def load_posted_tweets(self) -> List[Dict]:
        """Load previously posted tweets from log file"""
        try:
            if os.path.exists(self.tweets_log_file):
                with open(self.tweets_log_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            self.logger.error(f"Error loading tweets log: {e}")
            return []

    def save_posted_tweets(self):
        """Save posted tweets to log file"""
        try:
            with open(self.tweets_log_file, 'w', encoding='utf-8') as f:
                json.dump(self.posted_tweets, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving tweets log: {e}")

    def count_todays_tweets(self) -> int:
        """Count how many tweets were posted today"""
        today = datetime.now().date()
        count = 0
        for tweet in self.posted_tweets:
            tweet_date = datetime.fromisoformat(tweet['timestamp']).date()
            if tweet_date == today:
                count += 1
        return count

    def generate_tweet_with_gemini(self, content_type: str) -> Optional[str]:
        """Generate a tweet using Gemini AI based on content type"""
        prompts = {
            "ai_web_tips": """Generate a helpful AI web tip tweet for Ocliq (a web design/development company).
            Focus on practical advice about AI in web development, lead generation, or website optimization.
            Keep it under 280 characters. Make it engaging and informative.
            Example style: "Did you know your site can qualify leads automatically with AI?"
            Don't include hashtags or links unless specifically needed.""",

            "mini_threads": """Create the first tweet of a 2-3 part mini-thread about SEO or website strategy for Ocliq.
            This should be educational and valuable. Keep under 280 characters.
            Make it clear this is part 1 of a series. Focus on actionable insights.
            Example: "3 website mistakes killing your conversions (thread) 1/3"
            Don't include hashtags or links.""",

            "showcase_posts": """Create a showcase tweet highlighting how Ocliq helps businesses with web design/development.
            Focus on results and benefits. Keep under 280 characters.
            Example: "Check how we helped a real estate agency double leads with smart web design"
            Make it compelling but not overly salesy. Don't include links.""",

            "ctas": """Create a call-to-action tweet for Ocliq's services.
            Offer something valuable like a free consultation, mockup, or audit.
            Keep under 280 characters. Make it enticing but professional.
            Example: "Want a free homepage mockup? Try ocliq.com"
            Include the website link: https://ocliq.com""",

            "stats": """Create a tweet with an interesting statistic about web design, mobile optimization, or digital marketing.
            Make it relevant to Ocliq's services. Keep under 280 characters.
            Example: "88% of websites lose mobile leads due to bad design"
            Make the stat compelling and actionable. Don't include links."""
        }

        try:
            headers = {
                'Content-Type': 'application/json',
            }

            data = {
                "contents": [{
                    "parts": [{
                        "text": prompts[content_type]
                    }]
                }]
            }

            response = requests.post(
                f"{self.gemini_url}?key={self.gemini_api_key}",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    tweet_text = result['candidates'][0]['content']['parts'][0]['text'].strip()

                    # Clean up the tweet text
                    tweet_text = tweet_text.replace('"', '').replace("'", "'")

                    # Ensure it's under 280 characters
                    if len(tweet_text) > 280:
                        tweet_text = tweet_text[:277] + "..."

                    return tweet_text

            self.logger.error(f"Gemini API error: {response.status_code} - {response.text}")
            return None

        except Exception as e:
            self.logger.error(f"Error generating tweet with Gemini: {e}")
            return None

    def is_tweet_duplicate(self, tweet_text: str) -> bool:
        """Check if tweet is too similar to previously posted tweets"""
        # Simple duplicate check - can be enhanced with similarity algorithms
        for posted_tweet in self.posted_tweets:
            if posted_tweet['text'].lower() == tweet_text.lower():
                return True

            # Check for high similarity (simple word overlap check)
            posted_words = set(posted_tweet['text'].lower().split())
            new_words = set(tweet_text.lower().split())

            if len(posted_words) > 0 and len(new_words) > 0:
                overlap = len(posted_words.intersection(new_words))
                similarity = overlap / max(len(posted_words), len(new_words))
                if similarity > 0.8:  # 80% similarity threshold
                    return True

        return False

    def generate_tweet_queue(self, count: int = 20):
        """Generate a queue of tweets using different content types"""
        self.logger.info(f"Generating {count} new tweets...")

        generated_count = 0
        attempts = 0
        max_attempts = count * 3  # Allow multiple attempts per tweet

        while generated_count < count and attempts < max_attempts:
            attempts += 1

            # Rotate through content types
            content_type = self.content_types[self.current_content_index]
            self.current_content_index = (self.current_content_index + 1) % len(self.content_types)

            tweet_text = self.generate_tweet_with_gemini(content_type)

            if tweet_text and not self.is_tweet_duplicate(tweet_text):
                self.tweet_queue.append({
                    'text': tweet_text,
                    'content_type': content_type,
                    'generated_at': datetime.now().isoformat()
                })
                generated_count += 1
                self.logger.info(f"Generated tweet {generated_count}/{count}: {content_type}")
            else:
                if tweet_text:
                    self.logger.warning(f"Duplicate tweet detected, regenerating...")
                else:
                    self.logger.warning(f"Failed to generate tweet, retrying...")

            # Small delay between generation attempts
            time.sleep(1)

        self.logger.info(f"Generated {generated_count} unique tweets out of {attempts} attempts")

    def can_post_tweet(self) -> bool:
        """Check if we can post a tweet based on rate limits"""
        now = datetime.now()

        # Check daily limit
        if self.tweets_posted_today >= self.max_tweets_per_day:
            self.logger.info(f"Daily tweet limit reached ({self.max_tweets_per_day})")
            return False

        # Check hourly limit
        one_hour_ago = now - timedelta(hours=1)
        recent_tweets = [
            tweet for tweet in self.posted_tweets
            if datetime.fromisoformat(tweet['timestamp']) > one_hour_ago
        ]

        if len(recent_tweets) >= self.max_tweets_per_hour:
            self.logger.info(f"Hourly tweet limit reached ({self.max_tweets_per_hour})")
            return False

        # Check minimum delay between tweets
        if self.last_tweet_time:
            time_since_last = now - self.last_tweet_time
            min_delay = timedelta(minutes=self.min_delay_minutes)
            if time_since_last < min_delay:
                self.logger.info(f"Minimum delay not met. Last tweet: {time_since_last.total_seconds()/60:.1f} min ago")
                return False

        return True

    def post_tweet(self, tweet_data: Dict) -> bool:
        """Post a tweet to Twitter/X"""
        try:
            # Post the tweet
            response = self.twitter_client.create_tweet(text=tweet_data['text'])

            if response.data:
                # Log the successful post
                tweet_log = {
                    'id': response.data['id'],
                    'text': tweet_data['text'],
                    'content_type': tweet_data['content_type'],
                    'timestamp': datetime.now().isoformat(),
                    'generated_at': tweet_data['generated_at']
                }

                self.posted_tweets.append(tweet_log)
                self.save_posted_tweets()

                self.tweets_posted_today += 1
                self.last_tweet_time = datetime.now()

                self.logger.info(f"Tweet posted successfully: {tweet_data['text'][:50]}...")
                return True
            else:
                self.logger.error("Failed to post tweet - no response data")
                return False

        except Exception as e:
            self.logger.error(f"Error posting tweet: {e}")
            return False

    def get_next_delay(self) -> int:
        """Get random delay between tweets in seconds"""
        delay_minutes = random.randint(self.min_delay_minutes, self.max_delay_minutes)
        return delay_minutes * 60

    def preview_next_tweet(self) -> Optional[str]:
        """Preview the next tweet in queue without posting"""
        if self.tweet_queue:
            return self.tweet_queue[0]['text']
        return None

    def run_once(self) -> bool:
        """Run one iteration of the bot (try to post one tweet)"""
        # Check if we need to generate more tweets
        if len(self.tweet_queue) < self.min_queue_size:
            self.logger.info(f"Tweet queue low ({len(self.tweet_queue)}), generating more...")
            self.generate_tweet_queue(20)

        # Check if we can post
        if not self.can_post_tweet():
            return False

        # Check if we have tweets to post
        if not self.tweet_queue:
            self.logger.warning("No tweets in queue to post")
            return False

        # Get next tweet
        tweet_data = self.tweet_queue.pop(0)

        # Post the tweet
        success = self.post_tweet(tweet_data)

        if success:
            self.logger.info(f"Posted tweet. Queue size: {len(self.tweet_queue)}, Today's count: {self.tweets_posted_today}")

        return success

    def run_continuous(self):
        """Run the bot continuously"""
        self.logger.info("Starting Twitter bot in continuous mode...")

        # Generate initial tweet queue
        if len(self.tweet_queue) < self.min_queue_size:
            self.generate_tweet_queue(20)

        while True:
            try:
                # Reset daily counter if it's a new day
                current_date = datetime.now().date()
                if self.posted_tweets:
                    last_tweet_date = datetime.fromisoformat(self.posted_tweets[-1]['timestamp']).date()
                    if current_date > last_tweet_date:
                        self.tweets_posted_today = 0
                        self.logger.info("New day started, reset daily tweet counter")

                # Try to post a tweet
                posted = self.run_once()

                if posted:
                    # Calculate next delay
                    delay_seconds = self.get_next_delay()
                    self.logger.info(f"Next tweet in {delay_seconds//60} minutes")
                    time.sleep(delay_seconds)
                else:
                    # If we couldn't post, wait a shorter time before trying again
                    self.logger.info("Waiting 10 minutes before next attempt...")
                    time.sleep(600)  # 10 minutes

            except KeyboardInterrupt:
                self.logger.info("Bot stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error in main loop: {e}")
                self.logger.info("Waiting 5 minutes before retrying...")
                time.sleep(300)  # 5 minutes

    def run_scheduled(self):
        """Run the bot for scheduled execution (e.g., via cron)"""
        self.logger.info("Running Twitter bot in scheduled mode...")

        # Generate tweets if queue is low
        if len(self.tweet_queue) < self.min_queue_size:
            self.generate_tweet_queue(20)

        # Try to post one tweet
        success = self.run_once()

        if success:
            self.logger.info("Scheduled tweet posted successfully")
        else:
            self.logger.info("No tweet posted in this scheduled run")

        return success

    def status(self):
        """Print current bot status"""
        print(f"\n=== Twitter Bot Status ===")
        print(f"Queue size: {len(self.tweet_queue)}")
        print(f"Tweets posted today: {self.tweets_posted_today}/{self.max_tweets_per_day}")
        print(f"Total tweets posted: {len(self.posted_tweets)}")

        if self.last_tweet_time:
            time_since_last = datetime.now() - self.last_tweet_time
            print(f"Last tweet: {time_since_last.total_seconds()/60:.1f} minutes ago")
        else:
            print("Last tweet: Never")

        can_post = self.can_post_tweet()
        print(f"Can post now: {can_post}")

        if self.tweet_queue:
            print(f"\nNext tweet preview:")
            print(f"'{self.preview_next_tweet()}'")
        else:
            print("\nNo tweets in queue")

        print("=" * 27)


def main():
    """Main function to run the Twitter bot"""
    import argparse

    parser = argparse.ArgumentParser(description='Twitter Bot for @OcliqOfficial')
    parser.add_argument('--mode', choices=['continuous', 'scheduled', 'status', 'generate'],
                       default='continuous', help='Bot operation mode')
    parser.add_argument('--preview', action='store_true',
                       help='Preview next tweet without posting')
    parser.add_argument('--count', type=int, default=20,
                       help='Number of tweets to generate (for generate mode)')

    args = parser.parse_args()

    try:
        bot = TwitterBot()

        if args.mode == 'continuous':
            bot.run_continuous()
        elif args.mode == 'scheduled':
            bot.run_scheduled()
        elif args.mode == 'status':
            bot.status()
        elif args.mode == 'generate':
            bot.generate_tweet_queue(args.count)
            print(f"Generated {len(bot.tweet_queue)} tweets")

        if args.preview:
            preview = bot.preview_next_tweet()
            if preview:
                print(f"\nNext tweet preview:\n'{preview}'")
            else:
                print("\nNo tweets in queue to preview")

    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())