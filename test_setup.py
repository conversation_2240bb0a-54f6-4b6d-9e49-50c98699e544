#!/usr/bin/env python3
"""
Test script to verify Twitter bot setup
"""

import os
import sys
from dotenv import load_dotenv

def test_environment():
    """Test if environment variables are properly set"""
    print("🔍 Testing environment setup...")
    
    load_dotenv()
    
    required_vars = [
        'TWITTER_BEARER_TOKEN',
        'TWITTER_CONSUMER_KEY', 
        'TWITTER_CONSUMER_SECRET',
        'TWITTER_ACCESS_TOKEN',
        'TWITTER_ACCESS_TOKEN_SECRET',
        'GEMINI_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 Please check your .env file")
        return False
    else:
        print("✅ All environment variables are set")
        return True

def test_dependencies():
    """Test if required packages are installed"""
    print("\n🔍 Testing dependencies...")
    
    required_packages = ['tweepy', 'requests', 'dotenv']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Run: pip install -r requirements.txt")
        return False
    else:
        print("✅ All required packages are installed")
        return True

def test_twitter_connection():
    """Test Twitter API connection"""
    print("\n🔍 Testing Twitter API connection...")
    
    try:
        import tweepy
        load_dotenv()
        
        client = tweepy.Client(
            bearer_token=os.getenv('TWITTER_BEARER_TOKEN'),
            consumer_key=os.getenv('TWITTER_CONSUMER_KEY'),
            consumer_secret=os.getenv('TWITTER_CONSUMER_SECRET'),
            access_token=os.getenv('TWITTER_ACCESS_TOKEN'),
            access_token_secret=os.getenv('TWITTER_ACCESS_TOKEN_SECRET'),
            wait_on_rate_limit=True
        )
        
        me = client.get_me()
        print(f"✅ Twitter API connected successfully")
        print(f"   Account: @{me.data.username}")
        return True
        
    except Exception as e:
        print(f"❌ Twitter API connection failed: {e}")
        return False

def test_gemini_connection():
    """Test Gemini API connection"""
    print("\n🔍 Testing Gemini API connection...")
    
    try:
        import requests
        load_dotenv()
        
        api_key = os.getenv('GEMINI_API_KEY')
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}"
        
        data = {
            "contents": [{
                "parts": [{
                    "text": "Hello, this is a test."
                }]
            }]
        }
        
        response = requests.post(url, json=data, timeout=10)
        
        if response.status_code == 200:
            print("✅ Gemini API connected successfully")
            return True
        else:
            print(f"❌ Gemini API connection failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Gemini API connection failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Twitter Bot Setup Test\n")
    
    tests = [
        test_environment,
        test_dependencies,
        test_twitter_connection,
        test_gemini_connection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Your bot is ready to run.")
        print("\n🚀 Next steps:")
        print("   python twitterbot.py --mode status")
        print("   python twitterbot.py --mode generate --count 5")
        print("   python twitterbot.py --preview")
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
