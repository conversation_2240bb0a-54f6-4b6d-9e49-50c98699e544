#!/usr/bin/env python3
"""
Setup script for Twitter Bot
Helps users get started quickly
"""

import os
import subprocess
import sys

def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("📝 Creating .env file from template...")
            with open('.env.example', 'r') as src, open('.env', 'w') as dst:
                dst.write(src.read())
            print("✅ .env file created")
            print("⚠️  Please edit .env file with your actual API credentials")
            return True
        else:
            print("❌ .env.example not found")
            return False
    else:
        print("✅ .env file already exists")
        return True

def run_tests():
    """Run setup tests"""
    print("\n🧪 Running setup tests...")
    try:
        result = subprocess.run([sys.executable, "test_setup.py"], capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Twitter Bot Setup\n")
    
    steps = [
        ("Installing dependencies", install_dependencies),
        ("Creating environment file", create_env_file),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            return 1
    
    print("\n" + "="*50)
    print("🎉 Basic setup completed!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your API credentials:")
    print("   - Twitter API keys from https://developer.twitter.com")
    print("   - Gemini API key from https://makersuite.google.com")
    print("\n2. Test your setup:")
    print("   python test_setup.py")
    print("\n3. Try the bot:")
    print("   python twitterbot.py --mode status")
    print("   python twitterbot.py --mode generate --count 5")
    print("   python twitterbot.py --preview")
    print("\n4. Run the bot:")
    print("   python twitterbot.py --mode continuous")
    print("="*50)
    
    return 0

if __name__ == "__main__":
    exit(main())
