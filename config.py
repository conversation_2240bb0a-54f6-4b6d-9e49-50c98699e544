"""
Configuration settings for the Twitter Bot
Modify these values to customize bot behavior
"""

# Rate limiting settings
MAX_TWEETS_PER_HOUR = 2
MAX_TWEETS_PER_DAY = 48
MIN_DELAY_MINUTES = 20
MAX_DELAY_MINUTES = 40

# Queue management
MIN_QUEUE_SIZE = 10
INITIAL_QUEUE_SIZE = 20

# Content settings
BRAND_NAME = "Ocliq"
BRAND_WEBSITE = "https://ocliq.com"
TWITTER_HANDLE = "@OcliqOfficial"

# Duplicate detection
SIMILARITY_THRESHOLD = 0.8  # 80% similarity threshold for duplicate detection

# File paths
TWEETS_LOG_FILE = "tweets_log.json"
BOT_LOG_FILE = "twitter_bot.log"

# Content type weights (for future use - currently rotates equally)
CONTENT_TYPE_WEIGHTS = {
    "ai_web_tips": 1.0,
    "mini_threads": 1.0, 
    "showcase_posts": 1.0,
    "ctas": 1.0,
    "stats": 1.0
}

# API timeouts
GEMINI_TIMEOUT = 30
TWITTER_TIMEOUT = 30
