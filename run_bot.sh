#!/bin/bash

# Twitter Bot Runner Script
# This script can be used with cron for scheduled execution

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to the bot directory
cd "$SCRIPT_DIR"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Run the bot in scheduled mode
python twitterbot.py --mode scheduled

# Log the execution
echo "$(date): Bot execution completed" >> cron_execution.log
